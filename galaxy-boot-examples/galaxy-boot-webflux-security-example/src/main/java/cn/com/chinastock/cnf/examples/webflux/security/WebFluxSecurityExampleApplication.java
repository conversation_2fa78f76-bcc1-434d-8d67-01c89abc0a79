package cn.com.chinastock.cnf.examples.webflux.security;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Galaxy Boot WebFlux Security 示例应用
 * 
 * <p>该示例演示了如何在 WebFlux 应用中使用 Galaxy Boot Security 功能：</p>
 * <ul>
 *     <li>XSS 防护（输入和输出）</li>
 *     <li>CSRF 防护</li>
 *     <li>CORS 配置</li>
 *     <li>Actuator 端点保护</li>
 * </ul>
 * 
 * <AUTHOR> Assistant
 */
@SpringBootApplication
public class WebFluxSecurityExampleApplication {

    public static void main(String[] args) {
        SpringApplication.run(WebFluxSecurityExampleApplication.class, args);
    }
}
