package cn.com.chinastock.cnf.examples.webflux.security.controller;

import cn.com.chinastock.cnf.examples.webflux.security.model.UserRequest;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.web.reactive.server.WebTestClient;

/**
 * Security Demo Controller 集成测试
 * 
 * <AUTHOR> Assistant
 */
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureWebTestClient
@TestPropertySource(properties = {
        "galaxy.security.input-protect=true",
        "galaxy.security.output-protect=true",
        "galaxy.security.csrf.protect=false"
})
class SecurityDemoControllerTest {

    @Autowired
    private WebTestClient webTestClient;

    @Test
    void testPublicEndpoint() {
        webTestClient.get()
                .uri("/api/security/public")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.message").isEqualTo("This is a public endpoint");
    }

    @Test
    void testHealthEndpoint() {
        webTestClient.get()
                .uri("/api/security/health")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.status").isEqualTo("UP")
                .jsonPath("$.service").isEqualTo("WebFlux Security Example");
    }

    @Test
    void testXssInputProtection() {
        UserRequest request = new UserRequest();
        request.setName("<script>alert('xss')</script>");
        request.setEmail("<EMAIL>");
        request.setDescription("Normal description");

        webTestClient.post()
                .uri("/api/security/xss-input-test")
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.name").exists()
                .jsonPath("$.email").isEqualTo("<EMAIL>");
    }

    @Test
    void testXssOutputProtection() {
        webTestClient.get()
                .uri("/api/security/xss-output-test")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.id").isEqualTo(2)
                .jsonPath("$.email").isEqualTo("<EMAIL>");
    }

    @Test
    void testXssParamProtection() {
        webTestClient.get()
                .uri("/api/security/xss-param-test?name=<script>alert('xss')</script>&message=hello")
                .exchange()
                .expectStatus().isOk()
                .expectBody()
                .jsonPath("$.name").exists()
                .jsonPath("$.message").isEqualTo("hello");
    }

    @Test
    void testStringResponse() {
        webTestClient.get()
                .uri("/api/security/string-response")
                .exchange()
                .expectStatus().isOk()
                .expectBody(String.class)
                .value(response -> {
                    // 验证字符串响应被正确处理
                    assert response != null;
                    assert !response.contains("<script>");
                });
    }

    @Test
    void testUsersEndpoint() {
        webTestClient.get()
                .uri("/api/security/users")
                .exchange()
                .expectStatus().isOk()
                .expectBodyList(Object.class)
                .hasSize(2);
    }
}
