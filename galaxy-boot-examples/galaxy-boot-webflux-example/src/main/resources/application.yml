server:
  port: 8080

spring:
  application:
    name: galaxy-boot-webflux-example
  autoconfigure:
    exclude:
      - cn.com.chinastock.cnf.security.GalaxySecurityAutoConfiguration

galaxy:
  log:
    request-response:
      # 启用请求响应日志
      enabled: true
      # 启用 WebFlux 时序日志
      webflux-series: true
      # 启用请求头日志
      request-headers: true
      response-headers: true
      # 启用敏感字段掩码
      mask-field: true
    # 启用性能日志
    performance:
      enabled: true
    exception-pretty-print: true

  swagger:
    auth:
      username: admin     # Swagger UI 访问用户名
      password: P@5swOrd  # Swagger UI 访问密码

  security:
    user:
      name: user
      password: password
    input-protect: true
    output-protect: true
    actuator:
      protect: true
    #      whitelist:
    #        - 127.0.0.1
    #        - ***********/24    # 表示从 `***********` 到 `*************` 的所有 IP 地址。
    #        - 0:0:0:0:0:0:0:1  # 表示 `localhost` 的 IP 地址。
    csrf:
      protect: false
    #      whitelist:
    #        - /api/**
    #        - /swagger-ui/**
    #        - /v3/api-docs/**
    #      blacklist:
    #        - /api/security/csrf/**
    cors:
      protect: true
      whitelist:
        - https://*.chinastock.com.cn

logging:
  level:
    com.ctrip.framework: OFF