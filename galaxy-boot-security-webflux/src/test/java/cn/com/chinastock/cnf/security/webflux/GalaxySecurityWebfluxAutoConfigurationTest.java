package cn.com.chinastock.cnf.security.webflux;

import cn.com.chinastock.cnf.security.GalaxySecurityProperties;
import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.webflux.filter.XssWebFilter;
import cn.com.chinastock.cnf.security.webflux.handler.XssResponseBodyAdvice;
import org.junit.jupiter.api.Test;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ReactiveWebApplicationContextRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.server.SecurityWebFilterChain;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Galaxy Security WebFlux 自动配置测试
 * 
 * <AUTHOR> Assistant
 */
class GalaxySecurityWebfluxAutoConfigurationTest {

    private final ReactiveWebApplicationContextRunner contextRunner = new ReactiveWebApplicationContextRunner()
            .withConfiguration(AutoConfigurations.of(GalaxySecurityWebfluxAutoConfiguration.class));

    @Test
    void shouldCreateSecurityWebFilterChain() {
        contextRunner.run(context -> {
            assertThat(context).hasSingleBean(SecurityWebFilterChain.class);
            assertThat(context).hasSingleBean(PasswordEncoder.class);
        });
    }

    @Test
    void shouldCreateXssResponseBodyAdviceWhenOutputProtectEnabled() {
        contextRunner
                .withPropertyValues("galaxy.security.output-protect=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(XssResponseBodyAdvice.class);
                });
    }

    @Test
    void shouldNotCreateXssResponseBodyAdviceWhenOutputProtectDisabled() {
        contextRunner
                .withPropertyValues("galaxy.security.output-protect=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(XssResponseBodyAdvice.class);
                });
    }

    @Test
    void shouldCreateXssWebFilterWhenInputProtectEnabled() {
        contextRunner
                .withPropertyValues("galaxy.security.input-protect=true")
                .run(context -> {
                    assertThat(context).hasSingleBean(XssWebFilter.class);
                });
    }

    @Test
    void shouldNotCreateXssWebFilterWhenInputProtectDisabled() {
        contextRunner
                .withPropertyValues("galaxy.security.input-protect=false")
                .run(context -> {
                    assertThat(context).doesNotHaveBean(XssWebFilter.class);
                });
    }

    @Test
    void shouldLoadPropertiesCorrectly() {
        contextRunner
                .withPropertyValues(
                        "galaxy.security.input-protect=true",
                        "galaxy.security.output-protect=true",
                        "galaxy.security.user.name=testuser",
                        "galaxy.security.user.password=testpass",
                        "galaxy.security.cors.protect=true",
                        "galaxy.security.csrf.protect=true"
                )
                .run(context -> {
                    GalaxySecurityProperties properties = context.getBean(GalaxySecurityProperties.class);
                    assertThat(properties.isInputProtect()).isTrue();
                    assertThat(properties.isOutputProtect()).isTrue();
                    assertThat(properties.getUser().getName()).isEqualTo("testuser");
                    assertThat(properties.getUser().getPassword()).isEqualTo("testpass");
                    assertThat(properties.getCors().isProtect()).isTrue();
                    assertThat(properties.getCsrf().isProtect()).isTrue();
                });
    }
}
