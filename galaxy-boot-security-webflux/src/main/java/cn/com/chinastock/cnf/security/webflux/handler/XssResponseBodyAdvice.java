package cn.com.chinastock.cnf.security.webflux.handler;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import cn.com.chinastock.cnf.security.output.CopiedFastJsonProperties;
import cn.com.chinastock.cnf.security.output.FasterJsonFilterUtil;
import cn.com.chinastock.cnf.security.output.FastJsonFilterUtil;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.result.method.annotation.ResponseBodyAdvice;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.util.HtmlUtils;
import reactor.core.publisher.Mono;

/**
 * WebFlux XSS 响应体处理器
 * 
 * <p>该处理器在 WebFlux 环境下对响应体进行 XSS 过滤，支持：</p>
 * <ul>
 *     <li>JSON 响应体过滤（FastJson 和 Jackson）</li>
 *     <li>字符串响应体 HTML 转义</li>
 *     <li>响应式流处理</li>
 * </ul>
 * 
 * <AUTHOR> Assistant
 */
public class XssResponseBodyAdvice extends ResponseBodyResultHandler {

    private static final GalaxyLogger logger = GalaxyLogger.getLogger(XssResponseBodyAdvice.class);
    
    private final CopiedFastJsonProperties fastJsonProperties;

    public XssResponseBodyAdvice(CopiedFastJsonProperties fastJsonProperties) {
        super(null, null); // 这里需要传入实际的编码器和配置
        this.fastJsonProperties = fastJsonProperties;
        logger.info(LogCategory.SYSTEM_LOG, "XSS Response Body Advice initialized for WebFlux");
    }

    public XssResponseBodyAdvice(ServerCodecConfigurer codecConfigurer, 
                                CopiedFastJsonProperties fastJsonProperties) {
        super(codecConfigurer.getWriters(), codecConfigurer.getReaders());
        this.fastJsonProperties = fastJsonProperties;
        logger.info(LogCategory.SYSTEM_LOG, "XSS Response Body Advice initialized for WebFlux with codec configurer");
    }

    @Override
    public boolean supports(MethodParameter returnType, Class<?> converterType) {
        // 支持所有返回类型的处理
        return true;
    }

    @Override
    public Mono<Void> handleResult(ServerWebExchange exchange, 
                                  org.springframework.web.reactive.HandlerResult handlerResult) {
        Object returnValue = handlerResult.getReturnValue();
        
        if (returnValue == null) {
            return super.handleResult(exchange, handlerResult);
        }

        // 处理响应式类型
        if (returnValue instanceof Mono) {
            Mono<?> monoResult = (Mono<?>) returnValue;
            Mono<?> filteredResult = monoResult.map(this::filterResponseBody);
            
            // 创建新的 HandlerResult
            org.springframework.web.reactive.HandlerResult newHandlerResult = 
                new org.springframework.web.reactive.HandlerResult(
                    handlerResult.getHandler(), 
                    filteredResult, 
                    handlerResult.getReturnTypeSource()
                );
            
            return super.handleResult(exchange, newHandlerResult);
        }
        
        // 处理非响应式类型
        Object filteredValue = filterResponseBody(returnValue);
        org.springframework.web.reactive.HandlerResult newHandlerResult = 
            new org.springframework.web.reactive.HandlerResult(
                handlerResult.getHandler(), 
                filteredValue, 
                handlerResult.getReturnTypeSource()
            );
        
        return super.handleResult(exchange, newHandlerResult);
    }

    /**
     * 过滤响应体内容
     */
    private Object filterResponseBody(Object body) {
        if (body == null) {
            return null;
        }

        try {
            // 判断响应类型并进行相应处理
            if (isJsonResponse(body)) {
                return handleJsonResponse(body);
            } else if (body instanceof String) {
                return HtmlUtils.htmlEscape((String) body);
            }
            
            return body;
        } catch (Exception e) {
            logger.warn(LogCategory.SYSTEM_LOG, "Failed to filter response body, using original content", e);
            return body;
        }
    }

    /**
     * 判断是否为 JSON 响应
     */
    private boolean isJsonResponse(Object body) {
        // 简单判断：如果不是基本类型，认为是 JSON 对象
        return !(body instanceof String) && 
               !(body instanceof Number) && 
               !(body instanceof Boolean) &&
               !(body instanceof Character);
    }

    /**
     * 处理 JSON 响应
     */
    private Object handleJsonResponse(Object body) {
        try {
            // 尝试使用 FastJson 处理
            if (fastJsonProperties != null) {
                return FastJsonFilterUtil.handleFastJsonResponse(body, fastJsonProperties);
            } else {
                // 使用 Jackson 处理
                return FasterJsonFilterUtil.handleJacksonResponse(body);
            }
        } catch (Exception e) {
            logger.warn(LogCategory.SYSTEM_LOG, "Failed to handle JSON response with XSS filter", e);
            // 如果处理失败，尝试用 Jackson 处理
            try {
                return FasterJsonFilterUtil.handleJacksonResponse(body);
            } catch (Exception ex) {
                logger.warn(LogCategory.SYSTEM_LOG, "Failed to handle JSON response with Jackson filter", ex);
                return body;
            }
        }
    }
}
