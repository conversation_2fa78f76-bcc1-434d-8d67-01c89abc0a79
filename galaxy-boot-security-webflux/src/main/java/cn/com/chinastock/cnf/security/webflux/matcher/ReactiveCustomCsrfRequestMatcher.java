package cn.com.chinastock.cnf.security.webflux.matcher;

import cn.com.chinastock.cnf.core.log.GalaxyLogger;
import cn.com.chinastock.cnf.core.log.LogCategory;
import org.springframework.security.web.server.util.matcher.ServerWebExchangeMatcher;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

/**
 * WebFlux 环境下的自定义 CSRF 请求匹配器
 * 
 * <p>该匹配器支持：</p>
 * <ul>
 *     <li>白名单路径配置（不需要 CSRF 保护）</li>
 *     <li>黑名单路径配置（强制需要 CSRF 保护）</li>
 *     <li>Ant 风格路径匹配</li>
 *     <li>响应式匹配结果</li>
 * </ul>
 * 
 * <AUTHOR> Assistant
 */
public class ReactiveCustomCsrfRequestMatcher implements ServerWebExchangeMatcher {

    private static final GalaxyLogger logger = GalaxyLogger.getLogger(ReactiveCustomCsrfRequestMatcher.class);
    
    private final List<String> whitelistPatterns = new ArrayList<>();
    private final List<String> blacklistPatterns = new ArrayList<>();
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    /**
     * 构造函数
     * 
     * @param whitelist 白名单路径列表（不需要 CSRF 保护）
     * @param blacklist 黑名单路径列表（强制需要 CSRF 保护）
     */
    public ReactiveCustomCsrfRequestMatcher(List<String> whitelist, List<String> blacklist) {
        if (whitelist != null) {
            this.whitelistPatterns.addAll(whitelist);
        }
        
        if (blacklist != null) {
            this.blacklistPatterns.addAll(blacklist);
        }
        
        logger.info(LogCategory.SYSTEM_LOG, 
                   "Reactive CSRF Request Matcher initialized with whitelist: {}, blacklist: {}", 
                   whitelistPatterns, blacklistPatterns);
    }

    @Override
    public Mono<MatchResult> matches(ServerWebExchange exchange) {
        String requestPath = exchange.getRequest().getURI().getPath();
        
        return Mono.fromCallable(() -> {
            // 首先检查黑名单，如果匹配则需要 CSRF 保护
            for (String blacklistPattern : blacklistPatterns) {
                if (pathMatcher.match(blacklistPattern, requestPath)) {
                    logger.debug(LogCategory.SYSTEM_LOG, 
                               "Request path '{}' matches blacklist pattern '{}', CSRF protection required", 
                               requestPath, blacklistPattern);
                    return MatchResult.match();
                }
            }
            
            // 然后检查白名单，如果匹配则不需要 CSRF 保护
            for (String whitelistPattern : whitelistPatterns) {
                if (pathMatcher.match(whitelistPattern, requestPath)) {
                    logger.debug(LogCategory.SYSTEM_LOG, 
                               "Request path '{}' matches whitelist pattern '{}', CSRF protection not required", 
                               requestPath, whitelistPattern);
                    return MatchResult.notMatch();
                }
            }
            
            // 默认情况下需要 CSRF 保护
            logger.debug(LogCategory.SYSTEM_LOG, 
                       "Request path '{}' does not match any pattern, CSRF protection required by default", 
                       requestPath);
            return MatchResult.match();
        });
    }
}
